import * as GGSDK from "gg-game-sdk";

/**
 * OrangeSDKManager - Orange SDK integration wrapper
 * Handles tournament data storage and special status checking for Orange Games platform
 * Does NOT handle game progress saving/loading - only tournament data and special statuses
 */
export class OrangeSDKManager {
    constructor(config = {}) {
        this.config = {
            retryAttempts: config.retryAttempts || 3,
            retryDelay: config.retryDelay || 1000,
            debugMode: config.debugMode || this.isDebugEnvironment(),
            ...config
        };

        // SDK state
        this.isInitialized = false;
        this.isSDKAvailable = false;
        this.lastDataSendTime = 0;

        // User special status (loaded from Orange SDK)
        this.userSpecialStatus = null;
        this.defaultUserStatus = this.createDefaultUserStatus();

        // Tournament data to send
        this.tournamentData = {
            currentScore: 0,
            bestScore: 0,
            levelsCompleted: 0,
            totalPlayTime: 0,
            specialAchievements: [],
            lastPlayDate: null
        };

        // Event handlers
        this.eventHandlers = {
            onPause: null,
            onResume: null,
            onQuit: null,
            onStatusLoaded: null,
            onDataSent: null,
            onError: null
        };

        // Retry queue for failed operations
        this.retryQueue = [];
        this.retryTimer = null;

        console.log('OrangeSDKManager initialized for tournament data only');
    }
    
    /**
     * Check if running in debug environment
     * @returns {boolean} True if in debug mode
     */
    isDebugEnvironment() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.search.includes('debug=true');
    }
    
    /**
     * Initialize the Orange SDK and load user special status
     * @returns {Promise<boolean>} True if initialization successful
     */
    async init() {
        console.log('Initializing Orange SDK for tournament data...');

        try {
            // Check if SDK is available
            if (typeof GGSDK === 'undefined') {
                console.warn('Orange SDK not available, using default status');
                this.isSDKAvailable = false;
                this.userSpecialStatus = { ...this.defaultUserStatus };
                return this.handleOfflineMode();
            }

            this.isSDKAvailable = true;

            // Set up SDK event listeners
            this.setupEventListeners();

            // Load user special status from Orange SDK
            const statusLoaded = await this.loadUserSpecialStatus();

            // Send game loaded event
            this.sendGameLoadedEvent();

            this.isInitialized = true;
            console.log('Orange SDK initialized successfully');

            return statusLoaded;

        } catch (error) {
            console.error('Failed to initialize Orange SDK:', error);
            this.handleError('initialization_failed', error);
            return this.handleOfflineMode();
        }
    }
    
    /**
     * Handle offline mode when SDK is not available
     * @returns {boolean} Always returns true for offline mode
     */
    handleOfflineMode() {
        console.log('Running in offline mode - using local storage fallback');
        this.isInitialized = true;
        this.loadFromLocalStorage();
        return true;
    }
    
    /**
     * Set up Orange SDK event listeners
     */
    setupEventListeners() {
        try {
            // Listen for pause events from parent window
            GGSDK.listenPause(() => {
                console.log('Received pause event from Orange SDK');
                this.handlePauseEvent();
            });
            
            // Listen for resume events from parent window
            GGSDK.listenResume(() => {
                console.log('Received resume event from Orange SDK');
                this.handleResumeEvent();
            });
            
            // Listen for quit events from parent window
            GGSDK.listenQuit(() => {
                console.log('Received quit event from Orange SDK');
                this.handleQuitEvent();
            });
            
            console.log('Orange SDK event listeners set up successfully');
            
        } catch (error) {
            console.error('Failed to set up Orange SDK event listeners:', error);
            this.handleError('event_listener_setup_failed', error);
        }
    }
    
    /**
     * Load user special status from Orange SDK
     * @returns {Promise<boolean>} True if load successful
     */
    async loadUserSpecialStatus() {
        if (!this.isSDKAvailable) {
            this.userSpecialStatus = { ...this.defaultUserStatus };
            return true;
        }

        return new Promise((resolve) => {
            try {
                console.log('Loading user special status from Orange SDK...');

                GGSDK.getGameData(this.defaultUserStatus, (data) => {
                    console.log('User status loaded from Orange SDK:', data);

                    // Extract special status information
                    this.userSpecialStatus = this.parseUserStatus(data);

                    // Trigger status loaded callback
                    if (this.eventHandlers.onStatusLoaded) {
                        this.eventHandlers.onStatusLoaded(this.userSpecialStatus);
                    }

                    resolve(true);
                });

            } catch (error) {
                console.error('Failed to load user status from Orange SDK:', error);
                this.handleError('status_load_failed', error);
                this.userSpecialStatus = { ...this.defaultUserStatus };
                resolve(false);
            }
        });
    }
    
    /**
     * Send tournament data to Orange SDK
     * @param {object} data - Tournament data to send
     * @returns {Promise<boolean>} True if send successful
     */
    async sendTournamentData(data) {
        if (!data) {
            console.warn('No tournament data to send');
            return false;
        }

        // Update internal tournament data
        this.tournamentData = { ...this.tournamentData, ...data };
        this.tournamentData.lastPlayDate = Date.now();

        console.log('Sending tournament data to Orange SDK:', this.tournamentData);

        return this.performDataSend(this.tournamentData);
    }
    
    /**
     * Perform the actual data send operation with retry logic
     * @param {object} data - Tournament data to send
     * @returns {Promise<boolean>} True if send successful
     */
    async performDataSend(data) {
        try {
            const success = await this.sendWithRetry(data);

            if (success) {
                this.lastDataSendTime = Date.now();
                console.log('Tournament data sent successfully');

                // Trigger data sent callback
                if (this.eventHandlers.onDataSent) {
                    this.eventHandlers.onDataSent(data);
                }
            }

            return success;

        } catch (error) {
            console.error('Failed to send tournament data:', error);
            this.handleError('data_send_failed', error);
            return false;
        }
    }

    /**
     * Send tournament data with retry logic
     * @param {object} data - Tournament data to send
     * @param {number} attempt - Current attempt number
     * @returns {Promise<boolean>} True if send successful
     */
    async sendWithRetry(data, attempt = 1) {
        try {
            if (!this.isSDKAvailable) {
                console.log('SDK not available, tournament data not sent');
                return false;
            }

            console.log(`Sending tournament data to Orange SDK (attempt ${attempt}):`, data);

            // Send to Orange SDK
            GGSDK.saveGameData(data);

            return true;

        } catch (error) {
            console.error(`Send attempt ${attempt} failed:`, error);

            if (attempt < this.config.retryAttempts) {
                console.log(`Retrying send in ${this.config.retryDelay}ms...`);
                await this.delay(this.config.retryDelay);
                return this.sendWithRetry(data, attempt + 1);
            } else {
                console.error('All send attempts failed');
                this.handleError('send_failed', error);
                return false;
            }
        }
    }
    
    /**
     * Create default user status structure
     * @returns {object} Default user status
     */
    createDefaultUserStatus() {
        return {
            version: "1.0.0",
            playerId: null,
            specialStatuses: {
                consecutiveLoginDays: 0,
                hasExtraLife: false,
                hasSpeedBoost: false,
                hasDoubleTokens: false,
                hasShieldBoost: false,
                vipStatus: false,
                tournamentWinner: false
            },
            bonuses: {
                extraLives: 0,
                tokenMultiplier: 1.0,
                speedBoostDuration: 0,
                shieldBoostDuration: 0
            },
            lastLoginDate: null,
            statusExpiryDate: null,
            tournamentRank: null,
            specialBadges: []
        };
    }

    /**
     * Parse user status from Orange SDK data
     * @param {object} data - Raw data from Orange SDK
     * @returns {object} Parsed user status
     */
    parseUserStatus(data) {
        if (!data || typeof data !== 'object') {
            console.warn('Invalid user status data, using defaults');
            return { ...this.defaultUserStatus };
        }

        // Extract special status information
        const status = { ...this.defaultUserStatus };

        // Check for consecutive login bonus
        if (data.consecutiveLoginDays >= 2) {
            status.specialStatuses.consecutiveLoginDays = data.consecutiveLoginDays;
            status.specialStatuses.hasExtraLife = true;
            status.bonuses.extraLives = Math.floor(data.consecutiveLoginDays / 2);
        }

        // Check for tournament winner status
        if (data.tournamentWinner) {
            status.specialStatuses.tournamentWinner = true;
            status.specialStatuses.hasDoubleTokens = true;
            status.bonuses.tokenMultiplier = 2.0;
        }

        // Check for VIP status
        if (data.vipStatus) {
            status.specialStatuses.vipStatus = true;
            status.specialStatuses.hasSpeedBoost = true;
            status.specialStatuses.hasShieldBoost = true;
            status.bonuses.speedBoostDuration = 30000; // 30 seconds
            status.bonuses.shieldBoostDuration = 15000; // 15 seconds
        }

        // Copy other relevant data
        status.playerId = data.playerId;
        status.lastLoginDate = data.lastLoginDate;
        status.statusExpiryDate = data.statusExpiryDate;
        status.tournamentRank = data.tournamentRank;
        status.specialBadges = data.specialBadges || [];

        console.log('Parsed user special status:', status);
        return status;
    }

    /**
     * Validate tournament data before sending
     * @param {object} data - Tournament data to validate
     * @returns {object} Validated data
     */
    validateTournamentData(data) {
        const validated = { ...data };

        // Ensure required fields are present and valid
        if (typeof validated.currentScore !== 'number' || validated.currentScore < 0) {
            validated.currentScore = 0;
        }

        if (typeof validated.bestScore !== 'number' || validated.bestScore < 0) {
            validated.bestScore = 0;
        }

        if (typeof validated.levelsCompleted !== 'number' || validated.levelsCompleted < 0) {
            validated.levelsCompleted = 0;
        }

        if (typeof validated.totalPlayTime !== 'number' || validated.totalPlayTime < 0) {
            validated.totalPlayTime = 0;
        }

        if (!Array.isArray(validated.specialAchievements)) {
            validated.specialAchievements = [];
        }

        // Update last play date
        validated.lastPlayDate = Date.now();

        return validated;
    }

    /**
     * Handle pause event from Orange SDK
     */
    handlePauseEvent() {
        console.log('Handling pause event');

        // Send tournament data before pausing
        this.sendTournamentData({
            currentScore: this.tournamentData.currentScore,
            totalPlayTime: this.tournamentData.totalPlayTime
        });

        // Send pause event to SDK
        if (this.isSDKAvailable) {
            try {
                GGSDK.gamePaused();
            } catch (error) {
                console.error('Failed to send pause event to SDK:', error);
            }
        }

        // Trigger pause callback
        if (this.eventHandlers.onPause) {
            this.eventHandlers.onPause();
        }
    }

    /**
     * Handle resume event from Orange SDK
     */
    handleResumeEvent() {
        console.log('Handling resume event');

        // Send resume event to SDK
        if (this.isSDKAvailable) {
            try {
                GGSDK.gameResumed();
            } catch (error) {
                console.error('Failed to send resume event to SDK:', error);
            }
        }

        // Trigger resume callback
        if (this.eventHandlers.onResume) {
            this.eventHandlers.onResume();
        }
    }

    /**
     * Handle quit event from Orange SDK
     */
    handleQuitEvent() {
        console.log('Handling quit event');

        // Send final tournament data
        this.sendTournamentData({
            currentScore: this.tournamentData.currentScore,
            bestScore: this.tournamentData.bestScore,
            levelsCompleted: this.tournamentData.levelsCompleted,
            totalPlayTime: this.tournamentData.totalPlayTime
        });

        // Send game over event with current score
        this.sendGameOverEvent(this.tournamentData.currentScore);

        // Trigger quit callback
        if (this.eventHandlers.onQuit) {
            this.eventHandlers.onQuit();
        }
    }

    /**
     * Send game loaded event to Orange SDK
     */
    sendGameLoadedEvent() {
        if (this.isSDKAvailable) {
            try {
                GGSDK.gameLoaded();
                console.log('Game loaded event sent to Orange SDK');
            } catch (error) {
                console.error('Failed to send game loaded event:', error);
            }
        }
    }

    /**
     * Send game over event to Orange SDK
     * @param {number} score - Final score
     */
    sendGameOverEvent(score = 0) {
        if (this.isSDKAvailable) {
            try {
                GGSDK.gameOver(score);
                console.log(`Game over event sent to Orange SDK with score: ${score}`);
            } catch (error) {
                console.error('Failed to send game over event:', error);
            }
        }
    }

    /**
     * Update tournament score
     * @param {number} score - New score
     */
    updateScore(score) {
        this.tournamentData.currentScore = score;
        this.tournamentData.bestScore = Math.max(this.tournamentData.bestScore, score);
    }

    /**
     * Update levels completed
     * @param {number} levels - Number of levels completed
     */
    updateLevelsCompleted(levels) {
        this.tournamentData.levelsCompleted = levels;
    }

    /**
     * Update total play time
     * @param {number} time - Total play time in milliseconds
     */
    updatePlayTime(time) {
        this.tournamentData.totalPlayTime = time;
    }

    /**
     * Add special achievement
     * @param {string} achievement - Achievement identifier
     */
    addSpecialAchievement(achievement) {
        if (!this.tournamentData.specialAchievements.includes(achievement)) {
            this.tournamentData.specialAchievements.push(achievement);
        }
    }

    /**
     * Get user special status
     * @returns {object} User special status
     */
    getUserSpecialStatus() {
        return this.userSpecialStatus ? { ...this.userSpecialStatus } : null;
    }

    /**
     * Check if user has specific special status
     * @param {string} statusType - Type of status to check
     * @returns {boolean} True if user has the status
     */
    hasSpecialStatus(statusType) {
        return this.userSpecialStatus &&
               this.userSpecialStatus.specialStatuses &&
               this.userSpecialStatus.specialStatuses[statusType] === true;
    }

    /**
     * Get bonus value for specific type
     * @param {string} bonusType - Type of bonus
     * @returns {number} Bonus value
     */
    getBonusValue(bonusType) {
        return this.userSpecialStatus &&
               this.userSpecialStatus.bonuses &&
               this.userSpecialStatus.bonuses[bonusType] || 0;
    }

    /**
     * Handle errors with retry logic
     * @param {string} operation - Operation that failed
     * @param {Error} error - Error object
     */
    handleError(operation, error) {
        console.error(`Orange SDK error in ${operation}:`, error);

        // Trigger error callback
        if (this.eventHandlers.onError) {
            this.eventHandlers.onError(operation, error);
        }

        // Add to retry queue for certain operations
        if (operation === 'save_failed' && this.retryQueue.length < 10) {
            this.retryQueue.push({
                operation: 'save',
                data: this.gameData,
                timestamp: Date.now(),
                attempts: 0
            });

            this.scheduleRetry();
        }
    }

    /**
     * Schedule retry for failed operations
     */
    scheduleRetry() {
        if (this.retryTimer) {
            return; // Already scheduled
        }

        this.retryTimer = setTimeout(() => {
            this.processRetryQueue();
            this.retryTimer = null;
        }, this.config.retryDelay * 2); // Double delay for retries
    }

    /**
     * Process retry queue
     */
    async processRetryQueue() {
        const now = Date.now();
        const maxAge = 5 * 60 * 1000; // 5 minutes

        // Remove old entries
        this.retryQueue = this.retryQueue.filter(entry =>
            now - entry.timestamp < maxAge && entry.attempts < this.config.retryAttempts
        );

        // Process remaining entries
        for (const entry of this.retryQueue) {
            entry.attempts++;

            if (entry.operation === 'save') {
                const success = await this.performSave(entry.data);
                if (success) {
                    // Remove successful entry
                    const index = this.retryQueue.indexOf(entry);
                    if (index > -1) {
                        this.retryQueue.splice(index, 1);
                    }
                }
            }
        }

        // Schedule next retry if queue not empty
        if (this.retryQueue.length > 0) {
            this.scheduleRetry();
        }
    }

    /**
     * Utility function to create delay
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise} Promise that resolves after delay
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get current tournament data
     * @returns {object} Current tournament data
     */
    getTournamentData() {
        return { ...this.tournamentData };
    }

    /**
     * Send level completion data to tournament platform
     * @param {object} levelData - Level completion data
     */
    async sendLevelCompletion(levelData) {
        this.updateScore(levelData.score);
        this.updateLevelsCompleted(levelData.levelNumber);
        this.updatePlayTime(levelData.totalPlayTime);

        if (levelData.isPerfect) {
            this.addSpecialAchievement('perfect_level');
        }

        if (levelData.speedBonus) {
            this.addSpecialAchievement('speed_demon');
        }

        // Send updated tournament data
        return this.sendTournamentData(this.tournamentData);
    }

    /**
     * Set event handler for pause events
     * @param {Function} handler - Pause event handler
     */
    setOnPause(handler) {
        this.eventHandlers.onPause = handler;
    }

    /**
     * Set event handler for resume events
     * @param {Function} handler - Resume event handler
     */
    setOnResume(handler) {
        this.eventHandlers.onResume = handler;
    }

    /**
     * Set event handler for quit events
     * @param {Function} handler - Quit event handler
     */
    setOnQuit(handler) {
        this.eventHandlers.onQuit = handler;
    }

    /**
     * Set event handler for status loaded events
     * @param {Function} handler - Status loaded event handler
     */
    setOnStatusLoaded(handler) {
        this.eventHandlers.onStatusLoaded = handler;
    }

    /**
     * Set event handler for data sent events
     * @param {Function} handler - Data sent event handler
     */
    setOnDataSent(handler) {
        this.eventHandlers.onDataSent = handler;
    }

    /**
     * Set event handler for error events
     * @param {Function} handler - Error event handler
     */
    setOnError(handler) {
        this.eventHandlers.onError = handler;
    }

    /**
     * Get SDK status information
     * @returns {object} SDK status
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isSDKAvailable: this.isSDKAvailable,
            lastDataSendTime: this.lastDataSendTime,
            retryQueueLength: this.retryQueue.length,
            hasUserStatus: !!this.userSpecialStatus,
            tournamentDataReady: !!this.tournamentData
        };
    }

    /**
     * Reset tournament data
     */
    resetTournamentData() {
        console.log('Resetting tournament data');
        this.tournamentData = {
            currentScore: 0,
            bestScore: 0,
            levelsCompleted: 0,
            totalPlayTime: 0,
            specialAchievements: [],
            lastPlayDate: null
        };
    }

    /**
     * Clean up resources and timers
     */
    destroy() {
        console.log('Destroying OrangeSDKManager');

        // Send final tournament data
        if (this.isInitialized && this.tournamentData) {
            this.sendTournamentData(this.tournamentData);
        }

        // Clear timers
        if (this.retryTimer) {
            clearTimeout(this.retryTimer);
            this.retryTimer = null;
        }

        // Clear retry queue
        this.retryQueue = [];

        // Clear event handlers
        Object.keys(this.eventHandlers).forEach(key => {
            this.eventHandlers[key] = null;
        });

        this.isInitialized = false;
    }
}
